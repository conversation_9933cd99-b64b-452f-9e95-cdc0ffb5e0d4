// Sentry client-side plugin for additional configuration
export default function ({ $sentry, app, store }) {
  if (!$sentry) {
    console.warn('Sentry is not available')
    return
  }

  // Set user context when available
  if (store.state.user?.item) {
    $sentry.setUser({
      id: store.state.user.item.id,
      email: store.state.user.item.email,
      username: store.state.user.item.username,
      role: store.state.user.item.userType,
    })
  }

  // Set additional context
  $sentry.setContext('app', {
    name: 'langu-frontend-7b',
    version: process.env.npm_package_version || '1.0.0',
    environment: process.env.NUXT_ENV_SENTRY_ENVIRONMENT || process.env.NODE_ENV,
  })

  // Add route context on navigation
  app.router.afterEach((to) => {
    $sentry.addBreadcrumb({
      category: 'navigation',
      message: `Navigated to ${to.path}`,
      level: 'info',
      data: {
        from: app.router.currentRoute.path,
        to: to.path,
      },
    })

    // Set page context
    $sentry.setContext('page', {
      path: to.path,
      name: to.name,
      params: to.params,
      query: to.query,
    })
  })

  // Capture unhandled promise rejections
  if (process.client) {
    window.addEventListener('unhandledrejection', (event) => {
      $sentry.captureException(event.reason, {
        tags: {
          source: 'unhandledrejection',
        },
      })
    })
  }

  // Test Sentry integration (only in development)
  if (process.env.NODE_ENV === 'development') {
    console.log('Sentry initialized for langu-frontend-7b')
    
    // Add a test function to window for manual testing
    if (process.client) {
      window.testSentry = () => {
        $sentry.captureMessage('Test message from langu-frontend-7b', 'info')
        console.log('Test Sentry message sent')
      }
    }
  }
}
